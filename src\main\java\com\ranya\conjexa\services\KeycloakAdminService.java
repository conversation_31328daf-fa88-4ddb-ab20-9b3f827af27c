package com.ranya.conjexa.services;

import com.ranya.conjexa.config.KeycloakProvider;
import com.ranya.conjexa.entities.CreateUserRequest;
import jakarta.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.representations.idm.*;

import org.springframework.stereotype.Service;

import java.util.Collections;

@Service
@RequiredArgsConstructor
public class KeycloakAdminService {

    private final KeycloakProvider keycloakProvider;

    public String createKeycloakUser(CreateUserRequest request) {
        Keycloak keycloak = keycloakProvider.getInstance();

        // Création d’un nouvel utilisateur
        UserRepresentation user = new UserRepresentation();
        user.setUsername(request.getEmail());
        user.setEmail(request.getEmail());
        user.setFirstName(request.getPrenom());
        user.setLastName(request.getNom());
        user.setEnabled(true);

        // Envoi à Keycloak (dans le realm Conjexa)
        Response response = keycloak.realm("Conjexa")
                .users()
                .create(user);

        if (response.getStatus() != 201) {
            throw new RuntimeException("Échec de création dans Keycloak: " + response.getStatus());
        }

        // Récupération de l’ID Keycloak créé
        String userId = response.getLocation().getPath().replaceAll(".*/([^/]+)$", "$1");

        // Ajouter le mot de passe
        CredentialRepresentation credential = new CredentialRepresentation();
        credential.setTemporary(false);
        credential.setType(CredentialRepresentation.PASSWORD);
        credential.setValue(request.getPassword());

        keycloak.realm("Conjexa")
                .users()
                .get(userId)
                .resetPassword(credential);

        // Ajouter le rôle Realm (Admin / Manager / Employee)
        RoleRepresentation role = keycloak.realm("Conjexa")
                .roles()
                .get(request.getRole().name())
                .toRepresentation();

        keycloak.realm("Conjexa")
                .users()
                .get(userId)
                .roles()
                .realmLevel()
                .add(Collections.singletonList(role));

        return userId;
    }
}

