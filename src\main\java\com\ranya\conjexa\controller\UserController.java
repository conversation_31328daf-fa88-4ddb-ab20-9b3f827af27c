package com.ranya.conjexa.controller;

import com.ranya.conjexa.entities.CreateUserRequest;
import com.ranya.conjexa.entities.User;
import com.ranya.conjexa.services.KeycloakAdminService;
import com.ranya.conjexa.services.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/api/users")
public class UserController {
    private final UserService userService;
    private final com.ranya.conjexa.repository.UserRepository userRepository;
    private final KeycloakAdminService keycloakAdminService;

    public User createUser1(CreateUserRequest req) {
        // Étape 1 : créer l'utilisateur dans Keycloak
        String keycloakId = keycloakAdminService.createKeycloakUser(req);

        // Étape 2 : enregistrer dans PostgreSQL
        User user = new User();
        user.setNom(req.getNom());
        user.setPrenom(req.getPrenom());
        user.setEmail(req.getEmail());
        user.setTelephone(req.getTelephone());
        user.setPassword(req.getPassword());
        user.setRole(req.getRole());
        user.setStatus(true);
        user.setActive(false);
        user.setCreatedAt(LocalDateTime.now());

        return userRepository.save(user);
    }



    @Autowired
    public UserController(UserService userService, com.ranya.conjexa.repository.UserRepository userRepository, KeycloakAdminService keycloakAdminService) {
        this.userService = userService;
        this.userRepository = userRepository;
        this.keycloakAdminService = keycloakAdminService;
    }


    @GetMapping("/me")
    public User getCurrentUser() {
        return userService.getOrCreateUser();
    }



    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public List<User> getAllUsers() {
        return userService.getAllUsers();
    }

}
