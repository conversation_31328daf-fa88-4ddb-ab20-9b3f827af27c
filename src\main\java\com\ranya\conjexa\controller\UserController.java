package com.ranya.conjexa.controller;

import com.ranya.conjexa.entities.User;
import com.ranya.conjexa.services.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/users")
public class UserController {
    private final UserService userService;
    private final com.ranya.conjexa.repository.UserRepository userRepository;
    private final KeycloakAdminService keycloakAdminService;



    @Autowired
    public UserController(UserService userService, com.ranya.conjexa.repository.UserRepository userRepository, KeycloakAdminService keycloakAdminService) {
        this.userService = userService;
        this.userRepository = userRepository;
        this.keycloakAdminService = keycloakAdminService;
    }


    @GetMapping("/me")
    public User getCurrentUser() {
        return userService.getOrCreateUser();
    }



    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public List<User> getAllUsers() {
        return userService.getAllUsers();
    }

}
