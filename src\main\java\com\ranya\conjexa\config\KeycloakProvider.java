package com.ranya.conjexa.config;

import jakarta.annotation.PostConstruct;
import org.keycloak.OAuth2Constants;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.KeycloakBuilder;
import org.springframework.stereotype.Component;

@Component
public class KeycloakProvider {

    private Keycloak keycloak;

    @PostConstruct
    public void init() {
        this.keycloak = KeycloakBuilder.builder()
                .serverUrl("http://localhost:8085")
                .realm("master")
                .clientId("admin-cli")
                .username("ranya")
                .password("ranya")
                .grantType(OAuth2Constants.PASSWORD)
                .build();
    }

    public Keycloak getInstance() {
        return keycloak;
    }
}
