package com.ranya.conjexa.config;

import jakarta.annotation.PostConstruct;
import org.keycloak.OAuth2Constants;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.KeycloakBuilder;
import org.springframework.stereotype.Component;

@Component
public class KeycloakProvider {

    private Keycloak keycloak;

    @PostConstruct
    public void init() {
        this.keycloak = KeycloakBuilder.builder()
                .serverUrl("http://localhost:8085")
                .realm("master") // le realm de l'utilisateur admin (reste "master")
                .clientId("admin-cli")
                .username("ranya") // ✅ ton utilisateur admin dans master
                .password("mot_de_passe_de_ranya") // ✅ ton vrai mot de passe admin
                .grantType(OAuth2Constants.PASSWORD)
                .build();
    }

    public Keycloak getInstance() {
        return keycloak;
    }
}
