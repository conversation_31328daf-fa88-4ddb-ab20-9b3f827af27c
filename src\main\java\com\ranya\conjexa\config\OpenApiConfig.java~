package com.ranya.conjexa.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.*;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OpenApiConfig {
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info().title("Conjexa API").version("1.0"))
                .components(new Components().addSecuritySchemes("Keycloak", securityScheme()))
                .addSecurityItem(new SecurityRequirement().addList("Keycloak"));
    }

    @Bean
    public GroupedOpenApi publicApi() {
        return GroupedOpenApi.builder()
                .group("public")
                .pathsToMatch("/api/**")
                .build();
    }

    private SecurityScheme securityScheme() {
        return new SecurityScheme()
                .type(SecurityScheme.Type.OAUTH2)
                .description("OAuth2 with Keycloak")
                .flows(new OAuthFlows()
                        .authorizationCode(new OAuthFlow()
                                .authorizationUrl("http://localhost:8085/realms/conges/protocol/openid-connect/auth")
                                .tokenUrl("http://localhost:8085/realms/conges/protocol/openid-connect/token")
                                .scopes(new Scopes()
                                        .addString("openid", "OpenID scope"))
                        ));
    }
}
