package com.ranya.conjexa.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Map;

@RestController
@RequestMapping("/api/public")
public class PublicController {

    @GetMapping("/health")
    public Map<String, Object> health() {
        return Map.of(
            "status", "UP",
            "timestamp", LocalDateTime.now(),
            "message", "Conjexa API is running!"
        );
    }

    @GetMapping("/info")
    public Map<String, String> info() {
        return Map.of(
            "application", "Conjexa",
            "version", "1.0.0",
            "description", "User Management API with Keycloak Integration"
        );
    }
}
