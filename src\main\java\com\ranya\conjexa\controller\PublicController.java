package com.ranya.conjexa.controller;

import com.ranya.conjexa.entities.CreateUserRequest;
import com.ranya.conjexa.entities.User;
import com.ranya.conjexa.services.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Map;

@RestController
@RequestMapping("/api/public")
public class PublicController {

    private final UserService userService;

    @Autowired
    public PublicController(UserService userService) {
        this.userService = userService;
    }

    @GetMapping("/health")
    public Map<String, Object> health() {
        return Map.of(
            "status", "UP",
            "timestamp", LocalDateTime.now(),
            "message", "Conjexa API is running!"
        );
    }

    @GetMapping("/info")
    public Map<String, String> info() {
        return Map.of(
            "application", "Conjexa",
            "version", "1.0.0",
            "description", "User Management API with Keycloak Integration"
        );
    }

    @PostMapping("/users/create")
    public ResponseEntity<User> createUser(@RequestBody CreateUserRequest request) {
        User createdUser = userService.createUser(request);
        return ResponseEntity.ok(createdUser);
    }
}
