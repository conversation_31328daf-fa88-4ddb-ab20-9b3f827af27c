package com.ranya.conjexa.services;

import com.ranya.conjexa.entities.CreateUserRequest;
import com.ranya.conjexa.entities.Role;
import com.ranya.conjexa.entities.User;
import com.ranya.conjexa.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;
import org.springframework.security.core.Authentication;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class UserService {
    private final UserRepository userRepository;
    private final KeycloakAdminService keycloakAdminService;

    @Autowired
    public UserService(UserRepository userRepository, KeycloakAdminService keycloakAdminService) {
        this.userRepository = userRepository;
        this.keycloakAdminService = keycloakAdminService;
    }

    public User getOrCreateUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication == null || !(authentication.getPrincipal() instanceof Jwt)) {
            throw new IllegalStateException("Utilisateur non authentifié ou token invalide !");
        }

        Jwt jwt = (Jwt) authentication.getPrincipal();

        String email = jwt.getClaim("email");
        String firstName = jwt.getClaim("given_name");
        String lastName = jwt.getClaim("family_name");

        Map<String, List<String>> realmAccess = jwt.getClaim("realm_access");
        List<String> roles = realmAccess != null ? realmAccess.get("roles") : List.of();

        // Vérifie si l'utilisateur existe déjà par email
        User existing = userRepository.findAll()
                .stream()
                .filter(u -> u.getEmail().equalsIgnoreCase(email))
                .findFirst()
                .orElse(null);

        if (existing != null) {
            return existing;
        }

        // Création d'un nouvel utilisateur
        User newUser = new User();
        newUser.setEmail(email);
        newUser.setNom(firstName);
        newUser.setPrenom(lastName);
        newUser.setPassword(""); // pas stocké depuis Keycloak
        newUser.setRole(getMappedRole(roles));
        newUser.setStatus(true);
        newUser.setActive(false);

        return userRepository.save(newUser);
    }

    private Role getMappedRole(List<String> rolesFromToken) {
        if (rolesFromToken.contains("Admin")) return Role.Admin;
        if (rolesFromToken.contains("Manager")) return Role.Manager;
        return Role.Employee;
    }
    public List<User> getAllUsers() {
        return userRepository.findAll();
    }
    public User createUser(CreateUserRequest req) {
        // Étape 1 : créer l'utilisateur dans Keycloak
        String keycloakId = keycloakAdminService.createKeycloakUser(req);

        // Étape 2 : enregistrer dans PostgreSQL
        User user = new User();
        user.setNom(req.getNom());
        user.setPrenom(req.getPrenom());
        user.setEmail(req.getEmail());
        user.setTelephone(req.getTelephone());
        user.setPassword(req.getPassword()); // ⚠️ à hasher plus tard
        user.setRole(req.getRole());
        user.setStatus(true);
        user.setActive(false);
        user.setCreatedAt(java.time.LocalDateTime.now());

        return userRepository.save(user);
    }

}
